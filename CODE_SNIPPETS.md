# Snippets de Code - Portfolio RedFox

## 🚀 Snippets Prêts à l'Emploi

### Nouveau Projet - Template HTML

```html
<!-- Template pour ajouter un nouveau projet -->
<div class="project-item" 
     data-tags="JavaScript,React,API" 
     data-search-text="nom projet mots clés description technologies">
  
  <!-- Résumé cliquable -->
  <div class="project-summary" 
       data-project="nom-unique-projet" 
       role="button" 
       aria-expanded="false" 
       aria-controls="project-nom-unique-projet" 
       tabindex="0">
    
    <div class="project-pixelart" 
         role="img" 
         aria-label="Icône pixel art pour [Nom du Projet]"></div>
    <h3>[Nom du Projet]</h3>
    <span class="expand-arrow">▶</span>
  </div>

  <!-- Informations vue compacte -->
  <div class="project-compact-info">
    <p class="project-description">
      <i>[Description courte et accrocheuse du projet]</i>
    </p>
    <div class="project-meta">
      <span class="project-date">
        <span class="date-label">Début:</span> 
        <span class="date-value">[MM/YYYY]</span>
      </span>
      <span class="project-date">
        <span class="date-label">Fin:</span> 
        <span class="date-value">[MM/YYYY]</span>
      </span>
    </div>
    <a href="pages/projets.html" class="more-info-btn">+ d'infos</a>
  </div>

  <!-- Détails complets (vue étendue) -->
  <div class="project-details" id="project-nom-unique-projet">
    <div class="project-info">
      <h4>Description</h4>
      <p>[Description détaillée du projet, contexte, objectifs]</p>
      
      <h4>Technologies utilisées</h4>
      <div class="tech-tags">
        <span class="tech-tag">[Technologie 1]</span>
        <span class="tech-tag">[Technologie 2]</span>
        <span class="tech-tag">[Technologie 3]</span>
      </div>
      
      <h4>Fonctionnalités clés</h4>
      <ul>
        <li>[Fonctionnalité 1]</li>
        <li>[Fonctionnalité 2]</li>
        <li>[Fonctionnalité 3]</li>
      </ul>
      
      <div class="project-links">
        <a href="[URL_DEMO]" class="project-link" target="_blank">
          Voir la démo
        </a>
        <a href="[URL_GITHUB]" class="project-link" target="_blank">
          Code source
        </a>
      </div>
    </div>
    
    <div class="project-visual">
      <div class="project-image-placeholder">
        <!-- Image ou capture d'écran du projet -->
        <img src="images/[nom-projet].png" 
             alt="Capture d'écran de [Nom du Projet]"
             style="image-rendering: pixelated;">
      </div>
    </div>
  </div>
</div>
```

### Nouveau Composant Interactif

```javascript
/**
 * Template pour créer un nouveau composant interactif
 * Remplacer [ComponentName] par le nom réel du composant
 */
const init[ComponentName] = (() => {
  // Configuration
  const config = {
    animationDuration: 300,
    autoClose: true,
    keyboardSupport: true
  };

  // État du composant
  let isActive = false;
  let elements = {};

  // Sélection des éléments DOM
  const getElements = () => ({
    container: document.querySelector('.[component-name]-container'),
    trigger: document.querySelector('.[component-name]-trigger'),
    content: document.querySelector('.[component-name]-content'),
    closeBtn: document.querySelector('.[component-name]-close')
  });

  // Vérification de l'existence des éléments
  const validateElements = () => {
    elements = getElements();
    return elements.container && elements.trigger;
  };

  // Gestion des événements
  const bindEvents = () => {
    // Événement principal
    elements.trigger.addEventListener('click', handleToggle);
    
    // Support clavier
    if (config.keyboardSupport) {
      elements.trigger.addEventListener('keydown', handleKeydown);
      document.addEventListener('keydown', handleEscape);
    }

    // Fermeture automatique
    if (config.autoClose && elements.closeBtn) {
      elements.closeBtn.addEventListener('click', handleClose);
    }

    // Clic extérieur
    if (config.autoClose) {
      document.addEventListener('click', handleClickOutside);
    }
  };

  // Gestionnaires d'événements
  const handleToggle = (event) => {
    event.preventDefault();
    event.stopPropagation();
    
    if (isActive) {
      close();
    } else {
      open();
    }
  };

  const handleKeydown = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleToggle(event);
    }
  };

  const handleEscape = (event) => {
    if (event.key === 'Escape' && isActive) {
      close();
    }
  };

  const handleClose = (event) => {
    event.preventDefault();
    close();
  };

  const handleClickOutside = (event) => {
    if (isActive && 
        !elements.container.contains(event.target) && 
        !elements.trigger.contains(event.target)) {
      close();
    }
  };

  // Actions principales
  const open = () => {
    if (isActive) return;

    isActive = true;
    elements.container.classList.add('active');
    elements.trigger.setAttribute('aria-expanded', 'true');
    
    // Animation d'ouverture
    elements.content.style.display = 'block';
    requestAnimationFrame(() => {
      elements.content.classList.add('visible');
    });

    // Gestion du focus
    if (config.keyboardSupport) {
      elements.content.focus();
    }

    // Callback personnalisé
    onOpen();
  };

  const close = () => {
    if (!isActive) return;

    isActive = false;
    elements.container.classList.remove('active');
    elements.trigger.setAttribute('aria-expanded', 'false');
    
    // Animation de fermeture
    elements.content.classList.remove('visible');
    setTimeout(() => {
      elements.content.style.display = 'none';
    }, config.animationDuration);

    // Retour du focus
    if (config.keyboardSupport) {
      elements.trigger.focus();
    }

    // Callback personnalisé
    onClose();
  };

  // Callbacks personnalisables
  const onOpen = () => {
    // Logique spécifique à l'ouverture
    console.log('[ComponentName] opened');
  };

  const onClose = () => {
    // Logique spécifique à la fermeture
    console.log('[ComponentName] closed');
  };

  // Nettoyage
  const destroy = () => {
    if (!elements.trigger) return;

    elements.trigger.removeEventListener('click', handleToggle);
    elements.trigger.removeEventListener('keydown', handleKeydown);
    document.removeEventListener('keydown', handleEscape);
    document.removeEventListener('click', handleClickOutside);
    
    if (elements.closeBtn) {
      elements.closeBtn.removeEventListener('click', handleClose);
    }
  };

  // Initialisation
  const init = () => {
    if (!validateElements()) {
      console.warn('[ComponentName]: Required elements not found');
      return false;
    }

    bindEvents();
    console.log('[ComponentName] initialized');
    return true;
  };

  // API publique
  return {
    init,
    open,
    close,
    destroy,
    isActive: () => isActive
  };
})();
```

### Animation CSS Pixel Art

```css
/* Template d'animation pixel art */
@keyframes pixel-[animation-name] {
  0% {
    transform: translateY(0px) scale(1);
    opacity: 1;
  }
  25% {
    transform: translateY(-2px) scale(1.02);
    opacity: 0.9;
  }
  50% {
    transform: translateY(-4px) scale(1.05);
    opacity: 0.8;
  }
  75% {
    transform: translateY(-2px) scale(1.02);
    opacity: 0.9;
  }
  100% {
    transform: translateY(0px) scale(1);
    opacity: 1;
  }
}

/* Classe d'animation */
.[element-class] {
  animation: pixel-[animation-name] 2s ease-in-out infinite;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}

/* Animation au hover */
.[element-class]:hover {
  animation-duration: 0.5s;
  animation-iteration-count: 1;
}
```

### Formulaire avec Validation

```html
<!-- Template de formulaire avec validation -->
<form class="[form-name]-form" autocomplete="off" novalidate>
  <div class="form-group">
    <label for="[field-name]">[Label du champ] *</label>
    <input 
      type="text" 
      id="[field-name]" 
      name="[field-name]" 
      data-rules="required,minLength:3"
      aria-describedby="[field-name]-error"
      required>
    <div id="[field-name]-error" class="error-message" aria-live="polite"></div>
  </div>

  <div class="form-group">
    <label for="email">Email *</label>
    <input 
      type="email" 
      id="email" 
      name="email" 
      data-rules="required,email"
      aria-describedby="email-error"
      required>
    <div id="email-error" class="error-message" aria-live="polite"></div>
  </div>

  <div class="form-group">
    <label for="message">Message *</label>
    <textarea 
      id="message" 
      name="message" 
      rows="4" 
      data-rules="required,minLength:10"
      aria-describedby="message-error"
      required></textarea>
    <div id="message-error" class="error-message" aria-live="polite"></div>
  </div>

  <button type="submit" class="submit-btn">
    <span class="btn-text">Envoyer</span>
    <span class="btn-loading hidden">Envoi...</span>
  </button>
</form>
```

```javascript
// JavaScript pour la validation
const init[FormName]Validation = () => {
  const form = document.querySelector('.[form-name]-form');
  if (!form) return;

  const errorMessages = {
    required: 'Ce champ est obligatoire',
    email: 'Veuillez saisir un email valide',
    minLength: (min) => `Minimum ${min} caractères requis`
  };

  const showError = (field, rule, param = null) => {
    const errorDiv = document.getElementById(`${field.name}-error`);
    const message = typeof errorMessages[rule] === 'function' 
      ? errorMessages[rule](param)
      : errorMessages[rule];
    
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    field.classList.add('error');
    field.setAttribute('aria-invalid', 'true');
  };

  const clearError = (field) => {
    const errorDiv = document.getElementById(`${field.name}-error`);
    errorDiv.textContent = '';
    errorDiv.style.display = 'none';
    field.classList.remove('error');
    field.setAttribute('aria-invalid', 'false');
  };

  const validateField = (field) => {
    const rules = field.dataset.rules?.split(',') || [];
    const value = field.value.trim();

    clearError(field);

    for (const rule of rules) {
      const [ruleName, param] = rule.split(':');
      
      switch (ruleName) {
        case 'required':
          if (!value) {
            showError(field, 'required');
            return false;
          }
          break;
        case 'email':
          if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            showError(field, 'email');
            return false;
          }
          break;
        case 'minLength':
          if (value && value.length < parseInt(param)) {
            showError(field, 'minLength', param);
            return false;
          }
          break;
      }
    }
    return true;
  };

  // Validation en temps réel
  form.querySelectorAll('input, textarea').forEach(field => {
    field.addEventListener('blur', () => validateField(field));
    field.addEventListener('input', () => {
      if (field.classList.contains('error')) {
        validateField(field);
      }
    });
  });

  // Validation à la soumission
  form.addEventListener('submit', (event) => {
    event.preventDefault();
    
    const fields = form.querySelectorAll('input, textarea');
    let isValid = true;

    fields.forEach(field => {
      if (!validateField(field)) {
        isValid = false;
      }
    });

    if (isValid) {
      // Traitement du formulaire
      handleFormSubmit(form);
    } else {
      // Focus sur le premier champ en erreur
      const firstError = form.querySelector('.error');
      if (firstError) {
        firstError.focus();
      }
    }
  });
};

const handleFormSubmit = async (form) => {
  const submitBtn = form.querySelector('.submit-btn');
  const btnText = submitBtn.querySelector('.btn-text');
  const btnLoading = submitBtn.querySelector('.btn-loading');

  // État de chargement
  submitBtn.disabled = true;
  btnText.classList.add('hidden');
  btnLoading.classList.remove('hidden');

  try {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Simulation d'envoi
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Succès
    AriaAnnouncer.announce('Formulaire envoyé avec succès');
    form.reset();
    
  } catch (error) {
    // Erreur
    AriaAnnouncer.announce('Erreur lors de l\'envoi', 'assertive');
    console.error('Form submission error:', error);
    
  } finally {
    // Restaurer l'état du bouton
    submitBtn.disabled = false;
    btnText.classList.remove('hidden');
    btnLoading.classList.add('hidden');
  }
};
```

### Composant Modal

```css
/* Styles pour modal pixel art */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-overlay.active {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--gray-dark);
  border: 3px solid var(--orange);
  border-radius: 0;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  transform: scale(0.8) translateY(20px);
  transition: transform 0.3s ease;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

.modal-overlay.active .modal-content {
  transform: scale(1) translateY(0);
}

.modal-header {
  background: var(--orange);
  color: var(--black);
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid var(--orange-light);
}

.modal-close {
  background: transparent;
  border: none;
  color: var(--black);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
}

.modal-body {
  padding: 1.5rem;
  color: var(--white);
}
```

```javascript
// JavaScript pour modal
const ModalManager = (() => {
  let currentModal = null;

  const create = (title, content, options = {}) => {
    const overlay = createElement('div', 'modal-overlay');
    const modal = createElement('div', 'modal-content');
    const header = createElement('div', 'modal-header');
    const titleEl = createElement('h2', '', { style: 'margin: 0;' });
    const closeBtn = createElement('button', 'modal-close', { 
      'aria-label': 'Fermer la modal' 
    });
    const body = createElement('div', 'modal-body');

    titleEl.textContent = title;
    closeBtn.innerHTML = '&times;';
    body.innerHTML = content;

    header.appendChild(titleEl);
    header.appendChild(closeBtn);
    modal.appendChild(header);
    modal.appendChild(body);
    overlay.appendChild(modal);

    // Événements
    closeBtn.addEventListener('click', () => close());
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) close();
    });

    document.addEventListener('keydown', handleKeydown);

    return overlay;
  };

  const open = (title, content, options = {}) => {
    if (currentModal) close();

    currentModal = create(title, content, options);
    document.body.appendChild(currentModal);

    // Trap focus
    FocusManager.trapFocus(currentModal);

    requestAnimationFrame(() => {
      currentModal.classList.add('active');
    });

    AriaAnnouncer.announce(`Modal ouverte: ${title}`);
  };

  const close = () => {
    if (!currentModal) return;

    currentModal.classList.remove('active');
    FocusManager.releaseFocus(currentModal);

    setTimeout(() => {
      document.body.removeChild(currentModal);
      currentModal = null;
    }, 300);

    document.removeEventListener('keydown', handleKeydown);
    AriaAnnouncer.announce('Modal fermée');
  };

  const handleKeydown = (event) => {
    if (event.key === 'Escape') {
      close();
    }
  };

  return { open, close };
})();

// Utilisation
// ModalManager.open('Titre', '<p>Contenu HTML</p>');
```

---

*Ces snippets constituent une boîte à outils complète pour développer efficacement sur le portfolio RedFox.*
