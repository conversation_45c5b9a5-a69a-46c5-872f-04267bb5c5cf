# Patterns de Développement - Portfolio RedFox

## 🎨 Patterns CSS Avancés

### Système de Grille Responsive

```css
/* Pattern de grille adaptative */
.responsive-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  transition: all 0.3s ease;
}

/* Breakpoints standardisés */
@media (max-width: 900px) {
  .responsive-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.8rem;
  }
}

@media (max-width: 600px) {
  .responsive-grid {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
}
```

### Animations Pixel Art

```css
/* Pattern d'animation pixel-perfect */
@keyframes pixel-bounce {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

@keyframes pixel-glow {
  0%, 100% { 
    box-shadow: 0 0 0 var(--orange);
  }
  50% { 
    box-shadow: 0 0 8px var(--orange-light);
  }
}

/* Application avec timing pixel-art */
.pixel-element {
  animation: pixel-bounce 2s infinite ease-in-out;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}
```

### États Interactifs Cohérents

```css
/* Pattern d'états hover/focus uniforme */
.interactive-element {
  transition: all 0.18s ease;
  border: 2px solid var(--orange);
  background: var(--gray-medium);
  color: var(--white);
}

.interactive-element:hover,
.interactive-element:focus {
  background: var(--orange);
  color: var(--black);
  border-color: var(--orange-light);
  outline: none;
  transform: translateY(-1px);
}

.interactive-element:active {
  transform: translateY(0);
}
```

---

## 🔧 Patterns JavaScript

### Module Pattern Standard

```javascript
const ModuleName = (() => {
  // Variables privées
  let privateVar = null;
  const config = {
    animationDuration: 300,
    threshold: 0.15
  };

  // Méthodes privées
  const privateMethod = () => {
    // Logique interne
  };

  // Sélecteurs cachés
  const getElements = () => ({
    container: document.querySelector('.module-container'),
    triggers: document.querySelectorAll('.module-trigger'),
    targets: document.querySelectorAll('.module-target')
  });

  // Méthodes publiques
  const init = () => {
    const elements = getElements();
    if (!elements.container) return false;

    bindEvents(elements);
    return true;
  };

  const bindEvents = (elements) => {
    elements.triggers.forEach(trigger => {
      trigger.addEventListener('click', handleTriggerClick);
      trigger.addEventListener('keydown', handleKeydown);
    });
  };

  const handleTriggerClick = (event) => {
    event.preventDefault();
    // Logique de clic
  };

  const handleKeydown = (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleTriggerClick(event);
    }
  };

  // API publique
  return {
    init,
    // Autres méthodes publiques si nécessaire
  };
})();
```

### Observer Pattern pour Animations

```javascript
const AnimationObserver = (() => {
  const observers = new Map();

  const createObserver = (callback, options = {}) => {
    const defaultOptions = {
      threshold: 0.15,
      rootMargin: '0px'
    };

    return new IntersectionObserver(callback, {
      ...defaultOptions,
      ...options
    });
  };

  const observeElements = (selector, callback, options) => {
    const elements = document.querySelectorAll(selector);
    if (elements.length === 0) return;

    const observer = createObserver(callback, options);
    
    elements.forEach(element => {
      observer.observe(element);
    });

    observers.set(selector, observer);
  };

  const unobserve = (selector) => {
    const observer = observers.get(selector);
    if (observer) {
      observer.disconnect();
      observers.delete(selector);
    }
  };

  return {
    observeElements,
    unobserve
  };
})();

// Utilisation
AnimationObserver.observeElements('.section', (entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('visible');
    }
  });
});
```

### Gestionnaire d'État Global

```javascript
const StateManager = (() => {
  let state = {
    theme: 'dark',
    sidebarOpen: false,
    chatbotOpen: false,
    currentView: 'detailed'
  };

  const listeners = new Map();

  const setState = (key, value) => {
    const oldValue = state[key];
    state[key] = value;

    // Notifier les listeners
    if (listeners.has(key)) {
      listeners.get(key).forEach(callback => {
        callback(value, oldValue);
      });
    }

    // Persister certains états
    if (['theme', 'currentView'].includes(key)) {
      localStorage.setItem(`redfox_${key}`, value);
    }
  };

  const getState = (key) => state[key];

  const subscribe = (key, callback) => {
    if (!listeners.has(key)) {
      listeners.set(key, []);
    }
    listeners.get(key).push(callback);
  };

  const unsubscribe = (key, callback) => {
    if (listeners.has(key)) {
      const callbacks = listeners.get(key);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    }
  };

  // Initialisation depuis localStorage
  const init = () => {
    const savedTheme = localStorage.getItem('redfox_theme');
    const savedView = localStorage.getItem('redfox_currentView');

    if (savedTheme) state.theme = savedTheme;
    if (savedView) state.currentView = savedView;
  };

  return {
    setState,
    getState,
    subscribe,
    unsubscribe,
    init
  };
})();
```

---

## 🎯 Patterns d'Accessibilité

### Gestionnaire de Focus

```javascript
const FocusManager = (() => {
  let focusableElements = [];
  let currentIndex = 0;

  const getFocusableElements = (container = document) => {
    return container.querySelectorAll(
      'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])'
    );
  };

  const trapFocus = (container) => {
    focusableElements = Array.from(getFocusableElements(container));
    currentIndex = 0;

    if (focusableElements.length > 0) {
      focusableElements[0].focus();
    }

    container.addEventListener('keydown', handleKeydown);
  };

  const releaseFocus = (container) => {
    container.removeEventListener('keydown', handleKeydown);
    focusableElements = [];
  };

  const handleKeydown = (event) => {
    if (event.key === 'Tab') {
      event.preventDefault();

      if (event.shiftKey) {
        currentIndex = currentIndex === 0 
          ? focusableElements.length - 1 
          : currentIndex - 1;
      } else {
        currentIndex = currentIndex === focusableElements.length - 1 
          ? 0 
          : currentIndex + 1;
      }

      focusableElements[currentIndex].focus();
    }
  };

  return {
    trapFocus,
    releaseFocus
  };
})();
```

### Annonces ARIA

```javascript
const AriaAnnouncer = (() => {
  let announcer = null;

  const init = () => {
    announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    document.body.appendChild(announcer);
  };

  const announce = (message, priority = 'polite') => {
    if (!announcer) init();

    announcer.setAttribute('aria-live', priority);
    announcer.textContent = message;

    // Nettoyer après annonce
    setTimeout(() => {
      announcer.textContent = '';
    }, 1000);
  };

  return {
    announce
  };
})();

// Utilisation
AriaAnnouncer.announce('3 projets affichés');
AriaAnnouncer.announce('Erreur de connexion', 'assertive');
```

---

## 🔄 Patterns de Performance

### Debouncing et Throttling

```javascript
const PerformanceUtils = (() => {
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };

  const throttle = (func, limit) => {
    let inThrottle;
    return function(...args) {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  };

  return {
    debounce,
    throttle
  };
})();

// Utilisation pour la recherche
const handleSearch = PerformanceUtils.debounce((query) => {
  // Logique de recherche
}, 300);

// Utilisation pour le scroll
const handleScroll = PerformanceUtils.throttle(() => {
  // Logique de scroll
}, 100);
```

### Lazy Loading d'Images

```javascript
const LazyLoader = (() => {
  const imageObserver = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        img.classList.remove('lazy');
        imageObserver.unobserve(img);
      }
    });
  });

  const init = () => {
    const lazyImages = document.querySelectorAll('img[data-src]');
    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  };

  return { init };
})();
```

---

## 🎨 Patterns de Thème

### Système de Thème Avancé

```javascript
const ThemeManager = (() => {
  const themes = {
    dark: {
      '--orange': '#ff7f2a',
      '--orange-light': '#ff9900',
      '--gray-dark': '#222',
      '--gray-medium': '#444',
      '--black': '#000',
      '--white': '#f5f5f5'
    },
    light: {
      '--orange': '#ff7f2a',
      '--orange-light': '#ff8800',
      '--gray-dark': '#e8e8e8',
      '--gray-medium': '#d0d0d0',
      '--black': '#f8f8f8',
      '--white': '#333'
    }
  };

  const applyTheme = (themeName) => {
    const theme = themes[themeName];
    if (!theme) return;

    const root = document.documentElement;
    
    Object.entries(theme).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });

    document.body.className = themeName === 'light' 
      ? 'light-theme' 
      : '';

    StateManager.setState('theme', themeName);
  };

  const toggleTheme = () => {
    const currentTheme = StateManager.getState('theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    applyTheme(newTheme);
  };

  const init = () => {
    const savedTheme = StateManager.getState('theme');
    applyTheme(savedTheme);
  };

  return {
    applyTheme,
    toggleTheme,
    init
  };
})();
```

---

## 🧪 Patterns de Test

### Tests d'Accessibilité

```javascript
const AccessibilityTests = (() => {
  const testKeyboardNavigation = (container) => {
    const focusableElements = container.querySelectorAll(
      'a, button, input, textarea, [tabindex]:not([tabindex="-1"])'
    );

    return {
      hasFocusableElements: focusableElements.length > 0,
      allHaveTabIndex: Array.from(focusableElements).every(
        el => el.hasAttribute('tabindex') || ['A', 'BUTTON', 'INPUT', 'TEXTAREA'].includes(el.tagName)
      )
    };
  };

  const testAriaLabels = (container) => {
    const interactiveElements = container.querySelectorAll('button, [role="button"]');
    
    return Array.from(interactiveElements).every(el => 
      el.hasAttribute('aria-label') || 
      el.hasAttribute('aria-labelledby') ||
      el.textContent.trim().length > 0
    );
  };

  const testColorContrast = () => {
    // Simulation basique - en production, utiliser des outils dédiés
    const computedStyle = getComputedStyle(document.body);
    const bgColor = computedStyle.backgroundColor;
    const textColor = computedStyle.color;
    
    return {
      background: bgColor,
      text: textColor,
      // Ajouter calcul de contraste réel
    };
  };

  return {
    testKeyboardNavigation,
    testAriaLabels,
    testColorContrast
  };
})();
```

### Validation de Formulaires

```javascript
const FormValidator = (() => {
  const rules = {
    required: (value) => value.trim().length > 0,
    email: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    minLength: (min) => (value) => value.length >= min
  };

  const validate = (form) => {
    const errors = {};
    const inputs = form.querySelectorAll('input, textarea');

    inputs.forEach(input => {
      const fieldErrors = [];
      const value = input.value;
      const fieldRules = input.dataset.rules?.split(',') || [];

      fieldRules.forEach(rule => {
        const [ruleName, param] = rule.split(':');
        const validator = rules[ruleName];

        if (validator) {
          const isValid = param 
            ? validator(param)(value)
            : validator(value);

          if (!isValid) {
            fieldErrors.push(ruleName);
          }
        }
      });

      if (fieldErrors.length > 0) {
        errors[input.name] = fieldErrors;
      }
    });

    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  };

  return { validate };
})();
```

---

## 📱 Patterns Responsive

### Breakpoints Manager

```javascript
const BreakpointManager = (() => {
  const breakpoints = {
    mobile: 600,
    tablet: 900,
    desktop: 1200
  };

  const getCurrentBreakpoint = () => {
    const width = window.innerWidth;
    
    if (width <= breakpoints.mobile) return 'mobile';
    if (width <= breakpoints.tablet) return 'tablet';
    return 'desktop';
  };

  const onBreakpointChange = (callback) => {
    let currentBreakpoint = getCurrentBreakpoint();

    const handleResize = PerformanceUtils.throttle(() => {
      const newBreakpoint = getCurrentBreakpoint();
      if (newBreakpoint !== currentBreakpoint) {
        currentBreakpoint = newBreakpoint;
        callback(newBreakpoint);
      }
    }, 100);

    window.addEventListener('resize', handleResize);
    
    // Appel initial
    callback(currentBreakpoint);

    return () => window.removeEventListener('resize', handleResize);
  };

  return {
    getCurrentBreakpoint,
    onBreakpointChange,
    breakpoints
  };
})();
```

---

*Ces patterns constituent la base technique du portfolio RedFox. Ils garantissent cohérence, performance et maintenabilité.*
