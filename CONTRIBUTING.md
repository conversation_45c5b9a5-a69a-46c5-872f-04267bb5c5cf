# Guide des Bonnes Pratiques - Portfolio RedFox

## 📋 Table des Matières

1. [Philosophie du Projet](#philosophie-du-projet)
2. [Structure des Fichiers](#structure-des-fichiers)
3. [Conventions de Nommage](#conventions-de-nommage)
4. [Système de Thèmes](#système-de-thèmes)
5. [Accessibilité](#accessibilité)
6. [Composants Interactifs](#composants-interactifs)
7. [Ajout de Nouveaux Projets](#ajout-de-nouveaux-projets)
8. [Exemples de Code](#exemples-de-code)
9. [Checklist de Vérification](#checklist-de-vérification)

---

## 🎯 Philosophie du Projet

### Principes Fondamentaux

**Site Statique Sans Framework** : Le portfolio RedFox est volontairement développé en HTML/CSS/JavaScript vanilla pour :
- Maximiser les performances et la vitesse de chargement
- Assurer une compatibilité universelle
- Maintenir un contrôle total sur le code
- Faciliter la maintenance et les contributions

**Esthétique Pixel Art** : Le design s'inspire de l'univers rétro-gaming avec :
- Police monospace (Consolas, fallback vers Courier)
- Bordures nettes sans border-radius
- Animations subtiles et fluides
- Palette de couleurs cohérente orange/gris
- Rendu pixelisé pour les images (`image-rendering: pixelated`)

**Thème Clair/Sombre** : Système de thèmes basé sur les variables CSS pour une expérience utilisateur adaptable.

---

## 📁 Structure des Fichiers

```
portfolio/
├── index.html          # Page principale
├── style.css           # Styles globaux
├── script.js           # Logique interactive
├── pages/
│   └── projets.html    # Page détaillée des projets
└── images/
    ├── fox_transparent.png
    └── *.png           # Assets visuels
```

### Organisation du Code

**HTML** : Structure sémantique avec sections clairement définies
**CSS** : Organisation modulaire par composants
**JavaScript** : Architecture modulaire avec fonctions spécialisées

---

## 🏷️ Conventions de Nommage

### Classes CSS

**Format** : `kebab-case` exclusivement
```css
/* ✅ Correct */
.project-item
.contact-form
.sidebar-toggle

/* ❌ Incorrect */
.projectItem
.ContactForm
.sidebar_toggle
```

**Hiérarchie des Classes** :
```css
/* Composant principal */
.component-name

/* Éléments du composant */
.component-name-element

/* États et modificateurs */
.component-name.state-modifier
.component-name.compact-view
```

### IDs HTML

**Format** : `kebab-case` pour la cohérence
```html
<!-- ✅ Correct -->
<div id="chatbot-window">
<button id="theme-toggle">

<!-- ❌ Incorrect -->
<div id="chatbotWindow">
<button id="themeToggle">
```

### Variables JavaScript

**Format** : `camelCase` pour les variables et fonctions
```javascript
// ✅ Correct
const chatHistory = [];
const initNavigation = () => {};

// ❌ Incorrect
const chat_history = [];
const init_navigation = () => {};
```

**Format** : `UPPER_SNAKE_CASE` pour les constantes
```javascript
// ✅ Correct
const ANIMATION_DURATION = 1000;
const CLOUD_FUNCTION_URL = 'https://...';
```

---

## 🎨 Système de Thèmes

### Variables CSS

Le système de thèmes utilise les custom properties CSS :

```css
:root {
  --orange: #ff7f2a;
  --orange-light: #ff9900;
  --gray-dark: #222;
  --gray-medium: #444;
  --black: #000;
  --white: #f5f5f5;
  --accent: #fff;
}

/* Redéfinition pour le thème clair */
body.light-theme {
  --orange: #ff7f2a;
  --orange-light: #ff8800;
  --gray-dark: #e8e8e8;
  --gray-medium: #d0d0d0;
  --black: #f8f8f8;
  --white: #333;
  --accent: #222;
}
```

### Utilisation des Variables

**Toujours utiliser les variables** pour les couleurs :
```css
/* ✅ Correct */
background: var(--gray-dark);
color: var(--orange);
border: 2px solid var(--orange-light);

/* ❌ Incorrect */
background: #222;
color: #ff7f2a;
```

### Ajout de Nouvelles Couleurs

1. Définir dans `:root` ET `body.light-theme`
2. Utiliser un nom sémantique
3. Tester dans les deux thèmes

---

## ♿ Accessibilité

### Attributs ARIA Obligatoires

**Navigation** :
```html
<button 
  aria-label="Ouvrir/fermer le menu"
  aria-controls="sidebar-definitions"
  aria-expanded="false">
```

**Éléments Interactifs** :
```html
<div role="button" tabindex="0" aria-expanded="false">
<div role="img" aria-label="Description de l'image">
```

### Navigation Clavier

**Support obligatoire** pour :
- `Tab` / `Shift+Tab` : Navigation
- `Enter` / `Space` : Activation
- `Escape` : Fermeture des modales

**Implémentation** :
```javascript
element.addEventListener('keydown', (event) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault();
    // Action
  }
});
```

### Classes Utilitaires

```css
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

---

## ⚙️ Composants Interactifs

### Architecture Modulaire

Chaque composant suit ce pattern :
```javascript
const initComponentName = () => {
  // Sélection des éléments
  const elements = {
    button: document.getElementById('component-button'),
    container: document.querySelector('.component-container')
  };

  // Vérification d'existence
  if (!elements.button) return;

  // Fonctions internes
  const handleAction = () => {
    // Logique
  };

  // Event listeners
  elements.button.addEventListener('click', handleAction);
};
```

### Chatbot

**Fonctionnalités** :
- Ouverture/fermeture avec animations
- Historique des conversations
- Intégration API Cloud Function
- Support clavier complet

### Sidebar

**Fonctionnalités** :
- Toggle avec animation hamburger
- Fermeture automatique (clic extérieur)
- États ARIA synchronisés

### Filtrage des Projets

**Fonctionnalités** :
- Recherche textuelle en temps réel
- Vue compacte/détaillée
- Animations de transition
- Compteur de résultats

---

## 📝 Ajout de Nouveaux Projets

### Structure HTML Requise

```html
<div class="project-item" 
     data-tags="Tag1,Tag2,Tag3" 
     data-search-text="mots clés recherche">
  
  <div class="project-summary" 
       data-project="nom-projet" 
       role="button" 
       aria-expanded="false" 
       aria-controls="project-nom-projet" 
       tabindex="0">
    
    <div class="project-pixelart" 
         role="img" 
         aria-label="Icône pixel art pour le projet"></div>
    <h3>Nom du Projet</h3>
    <span class="expand-arrow">▶</span>
  </div>

  <!-- Vue compacte -->
  <div class="project-compact-info">
    <p class="project-description">
      <i>Description courte du projet.</i>
    </p>
    <div class="project-meta">
      <span class="project-date">
        <span class="date-label">Début:</span> 
        <span class="date-value">MM/YYYY</span>
      </span>
      <span class="project-date">
        <span class="date-label">Fin:</span> 
        <span class="date-value">MM/YYYY</span>
      </span>
    </div>
    <a href="pages/projets.html" class="more-info-btn">+ d'infos</a>
  </div>

  <!-- Vue détaillée -->
  <div class="project-details" id="project-nom-projet">
    <!-- Contenu détaillé -->
  </div>
</div>
```

### Attributs Obligatoires

- `data-tags` : Tags séparés par des virgules pour le filtrage
- `data-search-text` : Mots-clés pour la recherche textuelle
- `data-project` : Identifiant unique du projet
- `aria-*` : Attributs d'accessibilité complets

---

## 💻 Exemples de Code

### Ajout d'une Animation

```css
@keyframes nom-animation {
  0% { /* État initial */ }
  100% { /* État final */ }
}

.element {
  animation: nom-animation 0.3s ease-out;
}
```

### Nouveau Composant Interactif

```javascript
const initNouveauComposant = () => {
  const element = document.querySelector('.nouveau-composant');
  if (!element) return;

  const handleInteraction = (event) => {
    // Logique d'interaction
    event.preventDefault();
    
    // Mise à jour ARIA
    element.setAttribute('aria-expanded', 'true');
    
    // Animation
    element.classList.add('active');
  };

  // Support souris et clavier
  element.addEventListener('click', handleInteraction);
  element.addEventListener('keydown', (event) => {
    if (event.key === 'Enter' || event.key === ' ') {
      handleInteraction(event);
    }
  });
};

// Ajout à l'initialisation
document.addEventListener("DOMContentLoaded", () => {
  // ... autres initialisations
  initNouveauComposant();
});
```

---

## ✅ Checklist de Vérification

### Avant Soumission

**Code** :
- [ ] Respect des conventions de nommage
- [ ] Utilisation des variables CSS pour les couleurs
- [ ] Code JavaScript modulaire et documenté
- [ ] Pas de `console.log` oubliés

**Accessibilité** :
- [ ] Attributs ARIA appropriés
- [ ] Support navigation clavier
- [ ] Contraste suffisant (thèmes clair/sombre)
- [ ] Textes alternatifs pour les images

**Responsive** :
- [ ] Test sur mobile (< 900px)
- [ ] Test sur tablette (900px - 1200px)
- [ ] Test sur desktop (> 1200px)

**Thèmes** :
- [ ] Fonctionnel en thème sombre
- [ ] Fonctionnel en thème clair
- [ ] Transitions fluides entre thèmes

**Performance** :
- [ ] Images optimisées
- [ ] CSS/JS minifiés si nécessaire
- [ ] Pas de ressources externes non nécessaires

**Tests Fonctionnels** :
- [ ] Navigation fluide entre sections
- [ ] Chatbot opérationnel
- [ ] Filtrage des projets fonctionnel
- [ ] Formulaire de contact validé
- [ ] Sidebar responsive

### Outils de Test

**Accessibilité** : Utiliser les outils de développement du navigateur (onglet Accessibility)
**Responsive** : Tester avec les outils de développement (Device Toolbar)
**Performance** : Lighthouse pour les métriques de performance

---

## 🚀 Contribution

Pour contribuer efficacement :

1. **Fork** le repository
2. **Créer** une branche feature (`git checkout -b feature/nouvelle-fonctionnalite`)
3. **Respecter** ce guide de bonnes pratiques
4. **Tester** selon la checklist
5. **Commiter** avec des messages clairs
6. **Soumettre** une Pull Request

**Format des commits** :
```
type(scope): description courte

Description détaillée si nécessaire
```

Types : `feat`, `fix`, `docs`, `style`, `refactor`, `test`

---

*Ce guide évolue avec le projet. N'hésitez pas à proposer des améliorations !*
